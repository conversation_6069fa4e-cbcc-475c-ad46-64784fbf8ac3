-- Function to get user's recent moods for AI analysis
-- This function should be created in your Supabase database

CREATE OR REPLACE FUNCTION get_user_recent_moods(p_user_id TEXT, p_limit INTEGER DEFAULT 10)
RETURNS TABLE (
    id BIGINT,
    created_at TIMESTAMPTZ,
    user_id VARCHAR,
    mood VARCHAR,
    rating SMALLINT,
    is_shared_with_ai BOOLEAN,
    note TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.created_at,
        m.user_id,
        m.mood,
        m.rating,
        m.is_shared_with_ai,
        m.note
    FROM moodvide.moods m
    WHERE m.user_id = p_user_id
    ORDER BY m.created_at DESC
    LIMIT p_limit;
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_recent_moods(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_recent_moods(TEXT, INTEGER) TO service_role;
