package routes

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/google/generative-ai-go/genai"
	"github.com/joho/godotenv"
	"google.golang.org/api/option"
)

// SelfHelpTip represents a self-help tip from the database
type SelfHelpTip struct {
	ID        int64  `json:"id"`
	Title     string `json:"title"`
	Desc      string `json:"desc"`
	CreatedAt string `json:"created_at"`
}

// loadEnv loads environment variables from .env file
func loadEnv() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found or could not be loaded: %v", err)
	}
}

func Hello(c *fiber.Ctx) error {
	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func SelfHelpTips(c *fiber.Ctx) error {
	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Use RPC to call a PostgreSQL function that can access the moodvide schema
	apiURL := fmt.Sprintf("%srpc/get_self_help_tips", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, nil)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch data from Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Supabase API error: %s", string(body)),
		})
	}

	// Parse JSON response
	var tips []SelfHelpTip
	if err := json.Unmarshal(body, &tips); err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse response",
		})
	}

	// Return the tips
	return c.JSON(fiber.Map{
		"success": true,
		"data":    tips,
		"count":   len(tips),
	})
}

// FlutterRequest represents the request structure from Flutter app
type FlutterRequest struct {
	Data MoodData `json:"data"`
}

// MoodData represents the mood data from Flutter app
type MoodData struct {
	Note           string `json:"note"`
	IsSharedWithAI bool   `json:"isSharedWithAI"`
	Rating         int    `json:"rating"`
	Mood           string `json:"mood"`
	UserID         string `json:"userId"`
}

// JournalEntry represents the journal entry data from Flutter app
type JournalEntry struct {
	Entry  string `json:"entry"`
	UserID string `json:"userId"`
}

// AISession represents an AI conversation session
type AISession struct {
	SessionID    string `json:"session_id"`
	UserID       string `json:"user_id"`
	CreatedAt    string `json:"created_at"`
	IsNewSession bool   `json:"is_new_session"`
}

// AIMessage represents a single message in conversation
type AIMessage struct {
	Type      string                 `json:"type"`
	Content   string                 `json:"content"`
	CreatedAt string                 `json:"created_at"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// ConversationContext represents the full context for AI
type ConversationContext struct {
	Session     AISession   `json:"session"`
	Messages    []AIMessage `json:"messages"`
	UserContext struct {
		RecentMoods    []map[string]interface{} `json:"recent_moods"`
		RecentJournals []map[string]interface{} `json:"recent_journals"`
	} `json:"user_context"`
}

// UserMood represents a mood entry from the database
type UserMood struct {
	ID             int64  `json:"id"`
	CreatedAt      string `json:"created_at"`
	UserID         string `json:"user_id"`
	Mood           string `json:"mood"`
	Rating         int    `json:"rating"`
	IsSharedWithAI bool   `json:"is_shared_with_ai"`
	Note           string `json:"note"`
}

func AddMood(c *fiber.Ctx) error {
	// Debug: Print raw request body
	rawBody := c.Body()
	log.Printf("=== Raw Request Body ===")
	log.Printf("Raw Body: %s", string(rawBody))
	log.Printf("Content-Type: %s", c.Get("Content-Type"))
	log.Printf("========================")

	// Parse the JSON body with Flutter structure
	var flutterRequest FlutterRequest
	if err := c.BodyParser(&flutterRequest); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request body",
		})
	}

	// Extract the mood data from the Flutter request
	moodData := flutterRequest.Data

	// Print the received data to console
	log.Printf("=== Parsed Mood Data ===")
	log.Printf("Note: '%s'", moodData.Note)
	log.Printf("IsSharedWithAI: %t", moodData.IsSharedWithAI)
	log.Printf("Rating: %d", moodData.Rating)
	log.Printf("Mood: '%s'", moodData.Mood)
	log.Printf("UserID: '%s'", moodData.UserID)
	log.Printf("========================")

	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Create the data for the single JSON parameter function
	insertData := map[string]interface{}{
		"mood_data": map[string]interface{}{
			"user_id":           moodData.UserID,
			"mood":              moodData.Mood,
			"rating":            moodData.Rating,
			"is_shared_with_ai": moodData.IsSharedWithAI,
			"note":              moodData.Note,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(insertData)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare data",
		})
	}

	// Debug: Print what we're sending to Supabase
	log.Printf("=== Sending to Supabase ===")
	log.Printf("JSON Data: %s", string(jsonData))
	log.Printf("========================")

	// Insert into moodvide schema table
	apiURL := fmt.Sprintf("%srpc/insert_mood_entry", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to save data to Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.Printf("Supabase error: %s", string(body))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to save mood data: %s", string(body)),
		})
	}

	// Debug: Print Supabase response
	log.Printf("=== Supabase Response ===")
	log.Printf("Status: %d", resp.StatusCode)
	log.Printf("Response: %s", string(body))
	log.Printf("========================")

	log.Printf("Mood data saved successfully to database")

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Mood data saved successfully",
		"data":    moodData,
	})
}

func AddJournalEntry(c *fiber.Ctx) error {
	// Debug: Print raw request body
	rawBody := c.Body()
	log.Printf("=== Raw Journal Request Body ===")
	log.Printf("Raw Body: %s", string(rawBody))
	log.Printf("Content-Type: %s", c.Get("Content-Type"))
	log.Printf("===============================")

	// Parse the JSON body
	var journalEntry JournalEntry
	if err := c.BodyParser(&journalEntry); err != nil {
		log.Printf("Error parsing journal request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request body",
		})
	}

	// Print the received data to console
	log.Printf("=== Parsed Journal Entry ===")
	log.Printf("Entry: '%s'", journalEntry.Entry)
	log.Printf("UserID: '%s'", journalEntry.UserID)
	log.Printf("============================")

	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Create the data for the journal entry function
	insertData := map[string]interface{}{
		"journal_data": map[string]interface{}{
			"user_id": journalEntry.UserID,
			"entry":   journalEntry.Entry,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(insertData)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare data",
		})
	}

	// Debug: Print what we're sending to Supabase
	log.Printf("=== Sending Journal Data to Supabase ===")
	log.Printf("JSON Data: %s", string(jsonData))
	log.Printf("=======================================")

	// Insert into moodvide schema table
	apiURL := fmt.Sprintf("%srpc/insert_journal_entry", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to save journal entry to Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.Printf("Supabase journal entry error: %s", string(body))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to save journal entry: %s", string(body)),
		})
	}

	// Debug: Print Supabase response
	log.Printf("=== Supabase Journal Response ===")
	log.Printf("Status: %d", resp.StatusCode)
	log.Printf("Response: %s", string(body))
	log.Printf("================================")

	log.Printf("Journal entry saved successfully to database")

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Journal entry saved successfully",
		"data":    journalEntry,
	})
}

// Helper function to get or create AI session
func getOrCreateAISession(userID string) (*AISession, error) {
	loadEnv()
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return nil, fmt.Errorf("missing Supabase configuration")
	}

	// Create request data
	requestData := map[string]interface{}{
		"p_user_id": userID,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Call Supabase function
	apiURL := fmt.Sprintf("%srpc/get_or_create_ai_session", serverURL)
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call Supabase: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("supabase error: %s", string(body))
	}

	var session AISession
	if err := json.Unmarshal(body, &session); err != nil {
		return nil, fmt.Errorf("failed to parse session: %v", err)
	}

	return &session, nil
}

// Helper function to get conversation context
func getConversationContext(sessionID string, limit int) (*ConversationContext, error) {
	loadEnv()
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return nil, fmt.Errorf("missing Supabase configuration")
	}

	// Create request data
	requestData := map[string]interface{}{
		"p_session_id": sessionID,
		"p_limit":      limit,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Call Supabase function
	apiURL := fmt.Sprintf("%srpc/get_conversation_context", serverURL)
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call Supabase: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("supabase error: %s", string(body))
	}

	var context ConversationContext
	if err := json.Unmarshal(body, &context); err != nil {
		return nil, fmt.Errorf("failed to parse context: %v", err)
	}

	return &context, nil
}

// Helper function to save AI conversation
func saveAIConversation(sessionID, userID, userQuestion, aiResponse string, metadata map[string]interface{}) error {
	loadEnv()
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return fmt.Errorf("missing Supabase configuration")
	}

	// Create request data
	requestData := map[string]interface{}{
		"p_session_id":    sessionID,
		"p_user_id":       userID,
		"p_user_question": userQuestion,
		"p_ai_response":   aiResponse,
		"p_metadata":      metadata,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	// Call Supabase function
	apiURL := fmt.Sprintf("%srpc/save_ai_conversation", serverURL)
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to call Supabase: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("supabase error: %s", string(body))
	}

	log.Printf("AI conversation saved successfully")
	return nil
}

// Helper function to get user's recent moods
func getUserRecentMoods(userID string, limit int) ([]UserMood, error) {
	loadEnv()
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return nil, fmt.Errorf("missing Supabase configuration")
	}

	// Create request data
	requestData := map[string]interface{}{
		"p_user_id": userID,
		"p_limit":   limit,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// Call Supabase function
	apiURL := fmt.Sprintf("%srpc/get_user_recent_moods", serverURL)
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call Supabase: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("supabase error: %s", string(body))
	}

	var moods []UserMood
	if err := json.Unmarshal(body, &moods); err != nil {
		return nil, fmt.Errorf("failed to parse moods: %v", err)
	}

	return moods, nil
}

// Helper function to format mood data for AI analysis
func formatMoodsForAI(moods []UserMood) string {
	if len(moods) == 0 {
		return "No recent mood data available."
	}

	var moodAnalysis strings.Builder
	moodAnalysis.WriteString("User's Recent Mood History (last 10 entries):\n")

	for i, mood := range moods {
		// Convert rating to mood description based on Flutter logic
		var moodDesc string
		switch mood.Rating {
		case 1:
			moodDesc = "Mad"
		case 2:
			moodDesc = "Bad"
		case 3:
			moodDesc = "OK"
		case 4:
			moodDesc = "Happy"
		case 5:
			moodDesc = "Joy"
		default:
			moodDesc = mood.Mood // fallback to stored mood
		}

		moodAnalysis.WriteString(fmt.Sprintf("%d. %s - %s (Rating: %d/5)",
			i+1, mood.CreatedAt[:10], moodDesc, mood.Rating)) // Show just date part

		if mood.Note != "" {
			moodAnalysis.WriteString(fmt.Sprintf(" - Note: %s", mood.Note))
		}
		moodAnalysis.WriteString("\n")
	}

	return moodAnalysis.String()
}

// Helper function to build conversation history for AI prompt
func buildConversationPrompt(context *ConversationContext) string {
	if context == nil || len(context.Messages) == 0 {
		return ""
	}

	var prompt strings.Builder
	prompt.WriteString("\n\n--- Previous Conversation ---\n")

	// Add recent mood context if available
	if len(context.UserContext.RecentMoods) > 0 {
		prompt.WriteString("Recent mood entries:\n")
		for _, mood := range context.UserContext.RecentMoods {
			if moodStr, ok := mood["mood"].(string); ok {
				if rating, ok := mood["rating"].(float64); ok {
					prompt.WriteString(fmt.Sprintf("- Mood: %s (rating: %.0f/10)\n", moodStr, rating))
				}
			}
		}
		prompt.WriteString("\n")
	}

	// Add conversation messages
	for _, msg := range context.Messages {
		if msg.Type == "user_question" {
			prompt.WriteString(fmt.Sprintf("User: %s\n", msg.Content))
		} else if msg.Type == "ai_response" {
			prompt.WriteString(fmt.Sprintf("Assistant: %s\n", msg.Content))
		}
	}

	prompt.WriteString("--- End Previous Conversation ---\n\n")
	return prompt.String()
}

func AskQuestionAI(c *fiber.Ctx) error {
	type QuestionData struct {
		UserID        string `json:"userId"`
		Question      string `json:"question"`
		SystemMessage string `json:"systemMessage,omitempty"` // Optional system message
	}

	data := new(QuestionData)
	if err := c.BodyParser(data); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Cannot parse JSON",
		})
	}

	log.Println("Received question data:", data)

	// Get API key from environment
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "GEMINI_API_KEY not configured",
		})
	}

	// Create context
	ctx := context.Background()

	// Create client
	client, err := genai.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		log.Printf("Error creating Gemini client: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to initialize AI client",
		})
	}
	defer client.Close()

	// Get the model
	model := client.GenerativeModel("gemini-2.0-flash")

	// 1. Get or create AI session
	session, err := getOrCreateAISession(data.UserID)
	if err != nil {
		log.Printf("Failed to get AI session: %v", err)
		// Continue without memory (fallback)
	} else {
		log.Printf("Using AI session: %s (new: %t)", session.SessionID, session.IsNewSession)
	}

	// 2. Get conversation context if session exists
	var conversationHistory string = ""
	var isFirstQuestion bool = false
	if session != nil {
		context, err := getConversationContext(session.SessionID, 5) // Last 5 messages
		if err != nil {
			log.Printf("Failed to get conversation context: %v", err)
		} else {
			// Build conversation history for AI prompt
			conversationHistory = buildConversationPrompt(context)
			log.Printf("Retrieved %d previous messages", len(context.Messages))

			// Check if this is the first question (no previous messages)
			isFirstQuestion = len(context.Messages) == 0
		}
	}

	// 3. If this is the first question, fetch user's recent moods for analysis
	var moodAnalysisPrompt string = ""
	if isFirstQuestion || session == nil {
		log.Printf("First question detected, fetching user's recent moods for analysis")
		recentMoods, err := getUserRecentMoods(data.UserID, 10)
		if err != nil {
			log.Printf("Failed to fetch user moods: %v", err)
		} else {
			moodAnalysisPrompt = formatMoodsForAI(recentMoods)
			log.Printf("Retrieved %d recent moods for analysis", len(recentMoods))
		}
	}

	// 4. Set system instruction - use provided system message or default
	systemInstruction := `
	You are a compassionate and encouraging AI companion designed to support users' emotional wellbeing. Your primary goal is to provide uplifting, positive, and genuinely helpful responses that acknowledge users' feelings while gently guiding them toward hope and resilience.

Core principles:
- Always validate emotions without judgment - all feelings are valid
- Offer gentle encouragement and perspective when appropriate
- Focus on strengths, growth, and possibilities rather than problems
- Use warm, conversational language that feels like talking to a caring friend
- Provide practical, actionable suggestions when users seem open to them
- Celebrate small wins and progress, no matter how minor
- Remind users of their inherent worth and capability

Response guidelines:
- Keep responses concise but meaningful (2-4 sentences typically)
- Ask thoughtful follow-up questions to show genuine interest
- Share gentle reframes when users are stuck in negative thinking patterns
- Offer specific coping strategies, mindfulness techniques, or mood-boosting activities
- Use hopeful language: "This feeling will pass," "You've overcome challenges before," "Small steps count"
- Avoid toxic positivity - don't dismiss or minimize genuine struggles
- If someone expresses serious mental health concerns, gently encourage professional support

Remember: Your role is to be a supportive presence that helps users feel heard, valued, and gently encouraged toward their next positive step, however small it may be.
	`

	// If we have mood analysis data, include it in the system instruction
	if moodAnalysisPrompt != "" {
		systemInstruction += "\n\nIMPORTANT: The user is asking their first question. I have access to their recent mood history below. Please EXPLICITLY reference and analyze their mood patterns in your response. Mention specific trends you notice, acknowledge their recent emotional state, and provide personalized suggestions based on their actual mood data. Be encouraging and focus on positive patterns or growth opportunities.\n\n" + moodAnalysisPrompt
	}

	if data.SystemMessage != "" {
		systemInstruction = data.SystemMessage
		// Still include mood analysis even with custom system message
		if moodAnalysisPrompt != "" {
			systemInstruction += "\n\nIMPORTANT: Please EXPLICITLY reference and analyze the user's recent mood patterns in your response. Mention specific trends you notice and provide personalized suggestions based on their actual mood data.\n\n" + moodAnalysisPrompt
		}
	}
	model.SystemInstruction = genai.NewUserContent(genai.Text(systemInstruction))

	// 5. Build the complete prompt with conversation history
	var fullPrompt string
	if conversationHistory != "" {
		fullPrompt = conversationHistory + "\nCurrent question: " + data.Question
		log.Printf("Using conversation history in prompt")
	} else {
		fullPrompt = data.Question
		log.Printf("No conversation history available")
	}

	// Generate response with user message including conversation history
	resp, err := model.GenerateContent(ctx, genai.Text(fullPrompt))
	if err != nil {
		log.Printf("Error generating content: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to generate AI response",
		})
	}

	// Extract the response text
	var answer string
	if len(resp.Candidates) > 0 && len(resp.Candidates[0].Content.Parts) > 0 {
		if textPart, ok := resp.Candidates[0].Content.Parts[0].(genai.Text); ok {
			answer = string(textPart)
		}
	}

	if answer == "" {
		answer = "Sorry, I couldn't generate a response."
	}

	// 6. AFTER getting the AI response, save the conversation:
	if session != nil {
		err = saveAIConversation(session.SessionID, data.UserID, data.Question, answer, nil)
		if err != nil {
			log.Printf("Failed to save conversation: %v", err)
		} else {
			log.Printf("Conversation saved to session: %s", session.SessionID)
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success":       true,
		"message":       "Question processed successfully",
		"userId":        data.UserID,
		"question":      data.Question,
		"systemMessage": systemInstruction,
		"answer":        answer,
	})
}
